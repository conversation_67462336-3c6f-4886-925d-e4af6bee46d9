# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

### Development
- `npm run dev` - Start serverless offline development server (port 4000)
- `npm run deploy` - Deploy to AWS Lambda using Serverless Framework
- `npm test` - Run Jest tests

### Testing
- `python tests/test_payment_api.py` - Test payment link generation API
- `python tests/test_payment_frontend.py` - Test frontend integration with Firebase
- `python tests/test_webhook.py` - Test webhook processing
- `TEST_ENV=prod python tests/test_payment_frontend.py` - Test against production

## Architecture

This is a **serverless payment API** built with Node.js and deployed to AWS Lambda using the Serverless Framework. It integrates with:
- **Pagar.me** payment gateway for Brazilian payment processing
- **MySQL database** for data persistence (hosted on Railway)
- **Firebase Authentication** for user management (no local user table)

### Core Components

**Payment Link Handler** (`src/handlers/paymentLink.js`)
- Creates payment links via Pagar.me API
- Supports credit card (2x installments) and PIX payments
- Validates plans and prevents duplicate links
- Stores links in `payment_links` table with Firebase UID

**Webhook Handler** (`src/handlers/webhook.js`)
- Processes `order.paid` webhooks from Pagar.me
- Updates payment status and activates user plans
- Uses database transactions for consistency
- Deactivates free trial when paid plan is activated

**Database Utility** (`src/utils/db.js`)
- MySQL connection pooling
- Query execution helpers
- Plan activation logic

### Key Database Tables

**payment_links**: Stores Pagar.me payment links
- `user_id` (VARCHAR): Firebase UID
- `external_id` (VARCHAR): Pagar.me payment link ID
- `plan_id` (INT): References plans table
- `status` (VARCHAR): PENDING, paid, etc.

**user_plans**: Active user subscriptions
- `user_id` (VARCHAR): Firebase UID  
- `plan_id` (INT): References plans table
- `status` (VARCHAR): active, expired, etc.
- `end_date` (TIMESTAMP): NULL for unlimited plans

**plans**: Available subscription plans
- `duration_days` (INT): 0 for unlimited plans
- `plan_type` (VARCHAR): free_trial, monthly, etc.
- `price` (DECIMAL): Price in cents

### Firebase Integration

The API is **Firebase-first**:
- User IDs are Firebase UIDs (strings), not database integers
- User data comes from Firebase Auth, not local `users` table
- Payment links require `userData.email` from Firebase
- Webhooks link payments to Firebase UIDs via `customer.code`

### Environment Variables

Required in `.env` files:
- `PAGARME_API_KEY` - Pagar.me secret key
- `PAGARME_API_URL` - Pagar.me API base URL
- `MYSQL_HOST/PORT/USER/PASSWORD/DATABASE` - Database connection
- `ENV` - Environment identifier

### Serverless Configuration

The service uses different environment files:
- `.env.prod` (default/production)
- `.env.dev` (development)
- `.env.test` (testing)

Functions are deployed with individual packaging for optimization.

### Error Handling

**Payment Links**: Return 400 for validation errors, 500 for API failures
**Webhooks**: Always return 200 to prevent Pagar.me retries, use transactions with rollback

### Testing Strategy

Tests use Python to simulate different scenarios:
- API endpoint testing with Firebase payloads
- Webhook simulation with Pagar.me format
- Both development and production environment testing

The codebase follows Brazilian payment processing patterns and integrates deeply with Firebase for modern authentication flows.