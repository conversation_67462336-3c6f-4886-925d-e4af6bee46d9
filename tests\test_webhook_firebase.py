#!/usr/bin/env python3
"""
Test script para simular webhook do Pagar.me com Firebase UID
Baseado no payment link criado: pl_L8Z5AEO3ljmMne0C0KhyOvR9y1q6rP7k
"""
import requests
import json
import os
from datetime import datetime, timezone

# Configuration
SERVERLESS_ENDPOINT = "https://hc7c4r5f9c.execute-api.us-east-2.amazonaws.com"
WEBHOOK_ENDPOINT = f"{SERVERLESS_ENDPOINT}/webhooks/payment"

# Dados baseados no pagamento criado anteriormente
FIREBASE_UID = "apkKBkXVevMJjEYLLuuLFFeX3rU2"
PAYMENT_LINK_ID = "pl_L8Z5AEO3ljmMne0C0KhyOvR9y1q6rP7k"
CUSTOMER_EMAIL = "<EMAIL>"
CUSTOMER_NAME = "test_user"
PLAN_ID = 4

def create_webhook_payload():
    """
    Cria o payload do webhook simulando o formato do Pagar.me
    quando um pagamento é aprovado (order.paid)
    """
    webhook_payload = {
        "id": f"hook_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "type": "order.paid",
        "created_at": datetime.now(timezone.utc).isoformat(),
        "data": {
            "id": PAYMENT_LINK_ID,
            "code": PAYMENT_LINK_ID,  # Este é o campo usado no webhook.js como paymentCode
            "amount": 110,  # R$ 1,10 (em centavos)
            "currency": "BRL",
            "status": "paid",
            "payment_method": "credit_card",
            "paid_at": datetime.now(timezone.utc).isoformat(),
            "created_at": datetime.now(timezone.utc).isoformat(),
            "customer": {
                "id": "cus_v39DLP1IZIrpeJr6",
                "name": CUSTOMER_NAME,
                "email": CUSTOMER_EMAIL,
                "document": "12345678901",
                "type": "individual",
                "phone": {
                    "country_code": "55",
                    "area_code": "11",
                    "number": "999999999"
                }
            },
            "charges": [
                {
                    "id": f"ch_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    "code": PAYMENT_LINK_ID,  # Campo crítico - usado no webhook.js
                    "amount": 110,
                    "currency": "BRL", 
                    "status": "paid",
                    "payment_method": "credit_card",
                    "paid_at": datetime.now(timezone.utc).isoformat(),
                    "created_at": datetime.now(timezone.utc).isoformat(),
                    "last_transaction": {
                        "id": f"tran_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        "transaction_type": "credit_card",
                        "status": "paid",
                        "success": True,
                        "installments": 1,
                        "acquirer_name": "pagarme",
                        "acquirer_tid": "123456789",
                        "acquirer_nsu": "987654321",
                        "authorization_code": "ABC123"
                    }
                }
            ],
            "items": [
                {
                    "id": f"item_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    "type": "plan", 
                    "description": "Personalizado",
                    "amount": 110,
                    "quantity": 1
                }
            ],
            "shipping": {
                "amount": 0,
                "description": "Digital Product"
            }
        }
    }
    
    return webhook_payload

def test_webhook():
    """
    Testa o processamento do webhook
    """
    print("=" * 60)
    print("🔔 TESTE DE WEBHOOK DO PAGAR.ME - FIREBASE UID")
    print("=" * 60)
    print(f"Endpoint: {WEBHOOK_ENDPOINT}")
    print(f"Firebase UID: {FIREBASE_UID}")
    print(f"Payment Link ID: {PAYMENT_LINK_ID}")
    print(f"Customer Email: {CUSTOMER_EMAIL}")
    print(f"Plan ID: {PLAN_ID}")
    print()
    
    # Create webhook payload
    webhook_data = create_webhook_payload()
    
    print("📦 Webhook Payload:")
    print(json.dumps(webhook_data, indent=2, default=str))
    print()
    
    try:
        # Send webhook request
        print("🚀 Enviando webhook...")
        response = requests.post(
            WEBHOOK_ENDPOINT,
            json=webhook_data,
            headers={
                'Content-Type': 'application/json',
                'User-Agent': 'PagarMe-Webhook/1.0'
            },
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        print()
        
        # Parse response
        try:
            response_data = response.json()
            print("📄 Response:")
            print(json.dumps(response_data, indent=2))
            print()
            
            # Check if webhook was processed successfully
            if response.status_code == 200:
                if response_data.get('success'):
                    print("✅ Webhook processado com sucesso!")
                    print(f"Firebase UID: {response_data.get('firebase_uid')}")
                    print(f"Plan ID: {response_data.get('plan_id')}")
                    print("🎯 User plan ativado!")
                else:
                    print("❌ Webhook processado mas falhou:")
                    print(f"Error: {response_data.get('error')}")
            else:
                print(f"❌ Webhook falhou com status {response.status_code}")
                
        except json.JSONDecodeError:
            print("❌ Resposta não é JSON válido:")
            print(response.text)
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Erro ao enviar webhook: {e}")
        return False
    
    return True

def test_invalid_webhook():
    """
    Testa webhook com dados inválidos para verificar tratamento de erros
    """
    print("\n" + "=" * 60)
    print("🧪 TESTE DE WEBHOOK INVÁLIDO")
    print("=" * 60)
    
    # Test with invalid webhook type
    invalid_webhook = {
        "id": "hook_invalid",
        "type": "order.pending",  # Tipo não suportado
        "data": {
            "id": "invalid_id",
            "charges": []
        }
    }
    
    try:
        response = requests.post(
            WEBHOOK_ENDPOINT,
            json=invalid_webhook,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        response_data = response.json()
        print("Response:")
        print(json.dumps(response_data, indent=2))
        
        if response.status_code == 200 and not response_data.get('success'):
            print("✅ Webhook inválido corretamente rejeitado!")
        else:
            print("❌ Webhook inválido não foi tratado corretamente")
            
    except Exception as e:
        print(f"❌ Erro no teste de webhook inválido: {e}")

if __name__ == "__main__":
    print("🎯 Iniciando testes de webhook com Firebase UID...")
    print()
    
    # Test valid webhook
    success = test_webhook()
    
    # Test invalid webhook  
    test_invalid_webhook()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 Testes concluídos!")
    else:
        print("❌ Alguns testes falharam!")
    print("=" * 60) 