const { executeQuery } = require('../utils/db');

const processWebhook = async (event) => {
  try {
    // Parse the incoming webhook data
    const webhookData = JSON.parse(event.body);
    
    console.log('🔔 Webhook received - ID:', webhookData.id, 'Type:', webhookData.type);
    
    // Validate the webhook
    if (!webhookData.id || !webhookData.type || webhookData.type !== 'order.paid') {
      console.log('⚠️ Webhook ignored - Invalid data or unhandled type');
      return {
        statusCode: 200, // Return 200 even for unprocessed webhooks to avoid retries
        body: JSON.stringify({
          success: false,
          error: 'Invalid webhook data or unhandled webhook type'
        })
      };
    }
    
    // Get the payment info from webhook
    if (!webhookData.data.charges || webhookData.data.charges.length === 0) {
      console.log('⚠️ Webhook ignored - No charges found');
      return {
        statusCode: 200, // Return 200 to acknowledge receipt
        body: JSON.stringify({
          success: false,
          error: 'No charges found in webhook data'
        })
      };
    }
    
    const charge = webhookData.data.charges[0];
    const paymentCode = charge.code; // This is the external_id in payment_links
    const customerEmail = webhookData.data.customer.email;
    
    // Log payment processing start
    console.log(`💳 Processing payment ${paymentCode} for ${customerEmail}`);
    
    try {
      // SINGLE QUERY: Get payment link with plan info in one query using JOIN
      const results = await executeQuery(`
        SELECT pl.*, p.duration_days, p.plan_type AS plan_type_from_plan, p.name AS plan_name  
        FROM payment_links pl
        JOIN plans p ON (pl.plan_id = p.id OR (pl.plan_id IS NULL AND pl.plan_type = p.plan_type))
        WHERE pl.external_id = ?
        LIMIT 1
      `, [paymentCode]);
      
      if (!results || results.length === 0) {
        console.error(`❌ Payment link with code ${paymentCode} not found in database`);
        return {
          statusCode: 200, // Return 200 to acknowledge receipt
          body: JSON.stringify({
            success: false,
            error: 'Payment link not found'
          })
        };
      }
      
      const paymentLink = results[0];
      const planDurationDays = paymentLink.duration_days;
      const firebaseUid = paymentLink.user_id; // This is the Firebase UID
      
      console.log(`🔥 Found payment link for Firebase UID: ${firebaseUid}`);
      console.log(`📋 Plan: ${paymentLink.plan_name} (${planDurationDays} days)`);
      
      // Update payment link status - Mark as paid
      await executeQuery(
        'UPDATE payment_links SET status = ?, paid_at = NOW(), updated_at = NOW() WHERE id = ?',
        ['paid', paymentLink.id]
      );
      
      console.log(`✅ Payment link ${paymentLink.id} marked as paid`);
      
      // Calculate end date
      let endDate = null;
      if (planDurationDays > 0) {
        endDate = new Date();
        endDate.setDate(endDate.getDate() + planDurationDays);
      }
      
      // Format end date in MySQL compatible format YYYY-MM-DD
      let formattedEndDate = null;
      if (endDate) {
        formattedEndDate = endDate.toISOString().slice(0, 10);
        console.log(`📅 Plan will expire on: ${formattedEndDate}`);
      } else {
        console.log(`♾️ Plan has unlimited duration`);
      }
      
      // Get plan_id, either directly from paymentLink or lookup by plan_type
      const planId = paymentLink.plan_id;
      
      // UPSERT: Create or update user plan (Firebase UID compatible)
      await executeQuery(`
        INSERT INTO user_plans (user_id, plan_id, payment_id, status, start_date, end_date, created_at, updated_at)
        VALUES (?, ?, ?, 'active', NOW(), ?, NOW(), NOW())
        ON DUPLICATE KEY UPDATE 
          status = 'active',
          payment_id = VALUES(payment_id),
          start_date = VALUES(start_date),
          end_date = VALUES(end_date),
          updated_at = NOW()
      `, [firebaseUid, planId, paymentLink.id, formattedEndDate]);
      
      console.log(`🎯 User plan activated for Firebase UID: ${firebaseUid}`);
      
      // CRITICAL: Deactivate free trial plans when paid plan is activated
      // This ensures only one active plan per user and prioritizes paid plans
      if (planId && planId !== 1) { // Assuming plan_id 1 is free_trial
        console.log(`🔄 Deactivating free trial plans for Firebase UID: ${firebaseUid}`);
        
        // Deactivate any active free trial plans for this user
        const updateResult = await executeQuery(`
          UPDATE user_plans up
          JOIN plans p ON up.plan_id = p.id
          SET up.status = 'replaced_by_paid_plan', up.updated_at = NOW()
          WHERE up.user_id = ? 
            AND up.status = 'active' 
            AND p.plan_type = 'free_trial'
            AND up.plan_id != ?
        `, [firebaseUid, planId]);
        
        if (updateResult.affectedRows > 0) {
          console.log(`✅ ${updateResult.affectedRows} free trial plans deactivated for Firebase UID: ${firebaseUid}`);
        } else {
          console.log(`ℹ️ No free trial plans to deactivate for Firebase UID: ${firebaseUid}`);
        }
      }
      
      console.log(`🎉 Payment processed successfully for Firebase UID: ${firebaseUid}`);
      return {
        statusCode: 200,
        body: JSON.stringify({
          success: true,
          message: 'Payment processed successfully',
          firebase_uid: firebaseUid,
          plan_id: planId
        })
      };
    } catch (error) {
      console.error('💥 Error processing webhook:', error);
      return {
        statusCode: 200, // Still return 200 to avoid webhook retries
        body: JSON.stringify({
          success: false,
          error: error.message
        })
      };
    }
  } catch (error) {
    console.error('💥 Error parsing webhook:', error);
    return {
      statusCode: 200, // Still return 200 for parsing errors
      body: JSON.stringify({
        success: false,
        error: 'Error parsing webhook data'
      })
    };
  }
};

module.exports = {
  process: processWebhook
}; 