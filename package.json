{"name": "oca-api", "version": "1.0.0", "description": "Ocabooks API", "main": "src/index.js", "scripts": {"test": "jest", "deploy": "serverless deploy", "dev": "serverless offline"}, "dependencies": {"@aws-sdk/client-dynamodb": "^3.0.0", "@proak/common-utils": "^1.1.1", "@proak/constants": "^1.1.2", "@proak/lambda-handler": "1.2.7", "@proak/request-validator": "^1.1.10", "axios": "^1.6.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^5.1.0", "firebase-admin": "^13.4.0", "form-data": "^4.0.2", "mysql2": "^3.14.1", "twilio": "^5.7.2"}, "devDependencies": {"dotenv-cli": "^8.0.0", "jest": "^29.7.0", "serverless": "4.17.1", "serverless-offline": "^14.4.0"}}