const axios = require('axios');
const getConstant = require('@proak/constants');
const { 
  executeQuery, 
  createPaymentLink: createPaymentLinkDB,
  getPaymentLinkByPagarmeId 
} = require('../utils/db');

const createPaymentLink = async (event) => {
  try {
    const body = JSON.parse(event.body);
    const { planId, userId, userData } = body;
    
    console.log('Payment link request received:', { planId, userId, userData });

    // Validate required fields
    if (!userId) {
      console.error('User ID is required');
      return {
        statusCode: 400,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({
          success: false,
          error: "User ID is required"
        })
      };
    }

    if (!userData || !userData.email) {
      console.error('User data with email is required');
      return {
        statusCode: 400,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({
          success: false,
          error: "User data with email is required"
        })
      };
    }

    // Get plan details from database using the db utils
    const plans = await executeQuery(
      'SELECT * FROM plans WHERE id = ? AND is_active = true',
      [planId]
    );

    if (!plans.length) {
      console.error(`Plan not found or inactive: ${planId}`);
      return {
        statusCode: 400,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({
          success: false,
          error: "Plan not found or inactive"
        })
      };
    }

    const plan = plans[0];
    
    console.log('Found plan:', { 
      plan: { id: plan.id, name: plan.name, price: plan.price }
    });

    // Use the plan_type directly from the database
    const paymentPlanType = plan.plan_type;
    
    // Check for existing active payment link
    const existingLinks = await executeQuery(
      'SELECT * FROM payment_links WHERE user_id = ? AND plan_id = ? AND status = "PENDING"',
      [userId, planId]
    );

    if (existingLinks.length > 0) {
      console.log(`Found existing payment link for user ${userId} and plan ${planId}`);
      return {
        statusCode: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({
          success: true,
          data: {
            url: existingLinks[0].url,
            status: existingLinks[0].status,
            created_at: existingLinks[0].created_at
          }
        })
      };
    }

    // Use provided user data (Firebase user data)
    const customerName = userData.username || userData.displayName || "User";
    const customerEmail = userData.email;

    // Construct payment request payload
    const paymentRequest = {
      is_building: false,
      payment_settings: {
        credit_card_settings: {
          installments_setup: {
            interest_type: "simple",
            max_installments: 2,
            amount: plan.price,
            free_installments: 2,
            interest_rate: 0
          },
          operation_type: "auth_and_capture"
        },
        pix_settings: {
          expires_in: 864000,
          additional_information: [
            {
              name: plan.name,
              value: plan.description ? plan.description.substring(0, 100) : plan.name
            }
          ]
        },
        accepted_payment_methods: [
          "credit_card",
          "pix"
        ],
        statement_descriptor: "Oca AI"
      },
      customer_settings: {
        customer: {
          name: customerName,
          email: customerEmail,
          code: userId.toString(),
          type: "individual"
        }
      },
      cart_settings: {
        items: [
          {
            name: plan.name,
            description: plan.description || "Plano Oca AI",
            amount: plan.price,
            shipping_cost: 0,
            default_quantity: 1
          }
        ]
      },
      name: `${plan.name} - ${customerName}`,
      type: "order",
      max_paid_sessions: 1
    };

    // Call Pagar.me API
    const pagarmeApiUrl = getConstant('pagarme').PAGARME_API_URL;
    const pagarmeApiKey = getConstant('pagarme').PAGARME_API_KEY;
    
    const response = await axios.post(
      `${pagarmeApiUrl}paymentlinks`,
      paymentRequest,
      {
        timeout: 25000, // 25 seconds timeout (Lambda has 30s timeout)
        headers: {
          'accept': 'application/json',
          'content-type': 'application/json',
          'Authorization': `Basic ${Buffer.from(pagarmeApiKey + ':').toString('base64')}`
        }
      }
    );

    console.log('Pagar.me API response received, saving to database...');

    // Save the payment link to the database using db utils
    await executeQuery(
      `INSERT INTO payment_links 
       (user_id, plan_id, external_id, amount, plan_type, status, url, name, type, max_paid_sessions) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        userId,                 // Firebase UID
        planId,                 // Store plan_id 
        response.data.id,       // As external_id
        plan.price,
        paymentPlanType,
        'PENDING',
        response.data.url,
        `${plan.name} - ${customerName}`,
        'ORDER',
        1
      ]
    );

    console.log('Payment link saved successfully');

    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify({
        success: true,
        data: {
          url: response.data.url,
          status: 'created',
          created_at: new Date().toISOString(),
          pagarme_response: response.data // Include full Pagar.me API response
        }
      })
    };
  } catch (error) {
    console.error('Error creating payment link:', error);
    
    // Handle timeout errors specifically
    if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
      return {
        statusCode: 408,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Credentials': true,
        },
        body: JSON.stringify({
          success: false,
          error: 'Request timeout - please try again',
          details: 'Connection to payment service timed out'
        })
      };
    }
    
    return {
      statusCode: error.response?.status || 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
      },
      body: JSON.stringify({
        success: false,
        error: error.response?.data || error.message,
        stack: process.env.ENV === 'dev' ? error.stack : undefined
      })
    };
  }
};

// Add handler for OPTIONS requests (CORS preflight)
const handleOptions = async (event) => {
  return {
    statusCode: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent,Accept',
      'Access-Control-Allow-Methods': 'OPTIONS,POST',
      'Access-Control-Allow-Credentials': true,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ message: 'CORS preflight request successful' })
  };
};

module.exports = {
  create: createPaymentLink,
  options: handleOptions
}; 