import requests
import json

# Change this URL if your serverless-offline runs on a different port
url = 'http://localhost:4000/payment-links'

# Firebase user data example
firebase_uid = "firebase_uid_test_user_12345"  # Firebase UID format
plan_id = 2  # Oca Tradicional plan

# Example payload matching the actual API structure
payload = {
    "planId": plan_id,
    "userId": firebase_uid,  # Firebase UID
    "userData": {
        "username": "Test User",
        "email": "<EMAIL>",
        "displayName": "Test User"  # Firebase displayName
    }
}

print("=== Testing Payment API with Firebase UID ===")
print("Sending request to:", url)
print("With payload:", json.dumps(payload, indent=2))  # Pretty print the payload

response = requests.post(url, json=payload)

print(f"\nStatus code: {response.status_code}")
print(f"Response: {response.text}")

if response.status_code == 200:
    try:
        data = response.json()
        if data.get('success'):
            print("\n✅ Success! Payment link created")
            print(f"Payment URL: {data.get('data', {}).get('url', 'N/A')}")
        else:
            print("\n❌ API returned success=false")
            print(f"Error: {data.get('error', 'Unknown error')}")
    except json.JSONDecodeError:
        print("\n⚠️ Response is not valid JSON")
else:
    print(f"\n❌ HTTP Error {response.status_code}")
    print("Check:")
    print("1. Serverless API is running: npm run dev")
    print("2. Database connection is working")
    print("3. Plan ID exists in database")
    print("4. payment_links table supports Firebase UIDs") 