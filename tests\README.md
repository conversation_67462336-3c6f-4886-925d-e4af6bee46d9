# Payment API Tests - Firebase Integration

Este diretório contém testes atualizados para a API de pagamento que agora é totalmente compatível com Firebase Authentication.

## 🔥 Mudanças Firebase

### Antes (Sistema <PERSON>)
- User IDs eram integers: `userId: 1`
- Dados armazenados na tabela `users`
- Testes usavam payload complexo do Pagar.me

### Agora (Firebase)
- User IDs são Firebase UIDs: `userId: "firebase_uid_test_12345"`
- Usuários gerenciados pelo Firebase Auth
- Testes usam payload simplificado da API

## 📋 Testes Disponíveis

### `test_payment_frontend.py`
**Objetivo**: Testa a geração de payment links com dados Firebase
```bash
python tests/test_payment_frontend.py
```

**Payload de Teste**:
```json
{
  "planId": 2,
  "userId": "firebase_uid_test_12345",
  "userData": {
    "username": "test_user",
    "email": "<EMAIL>",
    "displayName": "Test User"
  }
}
```

### `test_payment_api.py`
**Objetivo**: Testa diretamente o endpoint com Firebase UID
```bash
python tests/test_payment_api.py
```

### `test_webhook.py`
**Objetivo**: Simula webhook do Pagar.me com Firebase UID
```bash
python tests/test_webhook.py
```

**Importante**: O `customer.code` contém o Firebase UID que será usado para ativar o plano.

## 🗄️ Compatibilidade Banco de Dados

### Tabelas Atualizadas para Firebase
- `payment_links.user_id`: `VARCHAR(128)` (Firebase UID)
- `user_plans.user_id`: `VARCHAR(128)` (Firebase UID)

### Tabela Removida
- `users`: Não existe mais, dados vêm do Firebase

## 🚀 Como Executar

1. **Iniciar API de Pagamento**:
```bash
cd oca-api
npm run dev
```

2. **Executar Testes**:
```bash
# Teste básico da API
python tests/test_payment_api.py

# Teste do frontend
python tests/test_payment_frontend.py

# Teste do webhook
python tests/test_webhook.py
```

3. **Teste em Produção**:
```bash
TEST_ENV=prod python tests/test_payment_frontend.py
```

## ✅ Verificações

Os testes verificam:
- ✅ API aceita Firebase UIDs
- ✅ Payment links são criados corretamente
- ✅ Webhooks processam Firebase UIDs
- ✅ User plans são ativados com Firebase UIDs
- ✅ Não há dependência da tabela `users`

## 🔧 Solução de Problemas

### Erro: "User not found"
- ❌ **Causa**: API ainda procura na tabela `users`
- ✅ **Solução**: API foi atualizada para usar dados do Firebase

### Erro: "Invalid user data"
- ❌ **Causa**: Payload não contém `userData.email`
- ✅ **Solução**: Sempre enviar email no `userData`

### Webhook falha
- ❌ **Causa**: `external_id` não encontrado
- ✅ **Solução**: Primeiro gerar payment link, depois testar webhook 