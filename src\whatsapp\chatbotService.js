// src/whatsapp/chatbotService.js
// Lógica principal de conversação do chatbot

const { sendWhatsAppMessage, sendWhatsAppMessageWithButtons, processButtonResponse } = require('./twilioService');
const { executeQuery } = require('../utils/db');
const { CHAPTERS, CHAPTER_NAMES, MESSAGES, CONVERSATION_STATES } = require('./constants');
const admin = require('firebase-admin');

// Estado das conversas em memória (pode ser movido para Redis depois)
const conversationStates = new Map();

/**
 * Função principal para processar mensagens do chatbot
 * @param {string} userId - ID do usuário (Firebase UID) - pode ser null inicialmente
 * @param {string} conversationId - ID da conversa (número do telefone)
 * @param {string} message - Mensagem recebida
 * @param {string} type - Tipo da mensagem ('text', 'button', 'audio')
 */
const handleMessage = async (userId, conversationId, message, type = 'text') => {
  try {
    console.log(`🤖 Processando mensagem na conversa ${conversationId}: ${message} (tipo: ${type})`);
    
    // Busca ou inicializa estado da conversa
    const state = getConversationState(conversationId);
    
    // Processa baseado no estado atual
    switch (state.currentState) {
      case CONVERSATION_STATES.WAITING_EMAIL:
        await handleEmailInput(conversationId, message, state);
        break;
        
      case CONVERSATION_STATES.WAITING_CHAPTER:
        await handleChapterSelection(conversationId, message, type, state);
        break;
        
      case CONVERSATION_STATES.WAITING_MESSAGE:
        await handleMessageInput(conversationId, message, type, state);
        break;
        
      default:
        // Estado inicial - solicita email
        await startConversation(conversationId, message, state);
        break;
    }
    
  } catch (error) {
    console.error('❌ Erro no handleMessage:', error);
    await sendWhatsAppMessage(conversationId, MESSAGES.ERROR);
  }
};

/**
 * Inicia uma nova conversa
 */
const startConversation = async (conversationId, message, state) => {
  try {
    // Se a mensagem parece ser um email, processa diretamente
    if (message.includes('@')) {
      await handleEmailInput(conversationId, message, state);
    } else {
      // Solicita email
      state.currentState = CONVERSATION_STATES.WAITING_EMAIL;
      updateConversationState(conversationId, state);
      
      await sendWhatsAppMessage(conversationId, 'Olá! Para começar, me informe seu email cadastrado:');
    }
  } catch (error) {
    console.error('❌ Erro ao iniciar conversa:', error);
    throw error;
  }
};

/**
 * Processa entrada de email
 */
const handleEmailInput = async (conversationId, email, state) => {
  try {
    console.log(`📧 Processando email: ${email}`);
    
    // Valida formato do email
    if (!isValidEmail(email)) {
      await sendWhatsAppMessage(conversationId, 'Por favor, informe um email válido:');
      return;
    }
    
    // Busca usuário no Firebase
    const firebaseUser = await findUserByEmail(email);
    if (!firebaseUser) {
      await sendWhatsAppMessage(conversationId, MESSAGES.EMAIL_NOT_FOUND);
      return;
    }
    
    // Atualiza estado com dados do usuário
    state.userId = firebaseUser.uid;
    state.userEmail = email;
    state.userName = firebaseUser.displayName || firebaseUser.email.split('@')[0];
    state.currentState = CONVERSATION_STATES.WAITING_CHAPTER;
    updateConversationState(conversationId, state);
    
    // Envia mensagem de boas-vindas com opções de capítulos
    await sendWhatsAppMessageWithButtons(
      conversationId,
      MESSAGES.WELCOME(state.userName),
      CHAPTERS
    );
    
  } catch (error) {
    console.error('❌ Erro ao processar email:', error);
    throw error;
  }
};

/**
 * Processa seleção de capítulo
 */
const handleChapterSelection = async (conversationId, message, type, state) => {
  try {
    console.log(`📚 Processando seleção de capítulo: ${message}`);
    
    let selectedChapter;
    
    if (type === 'button' || type === 'text') {
      // Processa resposta como botão/opção
      selectedChapter = processButtonResponse(message, CHAPTERS);
    }
    
    if (!selectedChapter) {
      await sendWhatsAppMessageWithButtons(
        conversationId,
        MESSAGES.INVALID_CHAPTER,
        CHAPTERS
      );
      return;
    }
    
    // Atualiza estado com capítulo selecionado
    state.selectedChapter = selectedChapter;
    state.selectedChapterId = CHAPTERS[selectedChapter];
    state.currentState = CONVERSATION_STATES.WAITING_MESSAGE;
    updateConversationState(conversationId, state);
    
    // Confirma seleção e solicita história
    await sendWhatsAppMessage(
      conversationId,
      MESSAGES.CHAPTER_SELECTED(selectedChapter)
    );
    
  } catch (error) {
    console.error('❌ Erro ao processar seleção de capítulo:', error);
    throw error;
  }
};

/**
 * Processa mensagem/história do usuário
 */
const handleMessageInput = async (conversationId, message, type, state) => {
  try {
    console.log(`💬 Processando mensagem do usuário: ${message.substring(0, 50)}...`);
    
    let messageText = message;
    
    // Se for áudio, aqui seria feita a transcrição (Azure Speech-to-Text)
    if (type === 'audio') {
      // TODO: Implementar transcrição de áudio
      messageText = await transcribeAudio(message);
    }
    
    // Salva mensagem no banco de dados
    await saveMessageToDatabase(
      state.userId,
      conversationId,
      messageText,
      state.selectedChapterId
    );
    
    // Envia confirmação e oferece continuar
    await sendWhatsAppMessageWithButtons(
      conversationId,
      MESSAGES.MESSAGE_SAVED(state.selectedChapter),
      CHAPTERS
    );
    
    // Volta para seleção de capítulo para próxima mensagem
    state.currentState = CONVERSATION_STATES.WAITING_CHAPTER;
    updateConversationState(conversationId, state);
    
  } catch (error) {
    console.error('❌ Erro ao processar mensagem:', error);
    throw error;
  }
};

/**
 * Busca usuário no Firebase por email
 */
const findUserByEmail = async (email) => {
  try {
    const auth = admin.auth();
    const userRecord = await auth.getUserByEmail(email);
    return userRecord;
  } catch (error) {
    if (error.code === 'auth/user-not-found') {
      return null;
    }
    throw error;
  }
};

/**
 * Salva mensagem no banco de dados
 */
const saveMessageToDatabase = async (userId, conversationId, messageText, chapterId) => {
  try {
    const query = `
      INSERT INTO chatbot_conversations (user_id, conversation_id, message_text, chapter_id, created_at)
      VALUES (?, ?, ?, ?, NOW())
    `;
    
    await executeQuery(query, [userId, conversationId, messageText, chapterId]);
    
    console.log(`✅ Mensagem salva no banco: usuário ${userId}, capítulo ${chapterId}`);
    
  } catch (error) {
    console.error('❌ Erro ao salvar mensagem no banco:', error);
    throw error;
  }
};

/**
 * Busca último capítulo usado pelo usuário
 */
const getLastChapter = async (userId, conversationId) => {
  try {
    const query = `
      SELECT chapter_id 
      FROM chatbot_conversations 
      WHERE user_id = ? AND conversation_id = ?
      ORDER BY created_at DESC 
      LIMIT 1
    `;
    
    const rows = await executeQuery(query, [userId, conversationId]);
    
    if (rows && rows.length > 0) {
      return rows[0].chapter_id;
    }
    
    return null;
    
  } catch (error) {
    console.error('❌ Erro ao buscar último capítulo:', error);
    return null;
  }
};

/**
 * Obtém estado da conversa
 */
const getConversationState = (conversationId) => {
  if (!conversationStates.has(conversationId)) {
    conversationStates.set(conversationId, {
      userId: null,
      userEmail: null,
      userName: null,
      selectedChapter: null,
      selectedChapterId: null,
      currentState: CONVERSATION_STATES.WAITING_EMAIL,
      lastActivity: new Date()
    });
  }
  
  return conversationStates.get(conversationId);
};

/**
 * Atualiza estado da conversa
 */
const updateConversationState = (conversationId, newState) => {
  newState.lastActivity = new Date();
  conversationStates.set(conversationId, newState);
};

/**
 * Valida formato de email
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Transcreve áudio (placeholder para Azure Speech-to-Text)
 */
const transcribeAudio = async (audioData) => {
  // TODO: Implementar integração com Azure Speech-to-Text
  console.log('🎤 Transcrição de áudio não implementada ainda');
  return '[Áudio recebido - transcrição pendente]';
};

module.exports = {
  handleMessage,
  getLastChapter,
  saveMessageToDatabase,
  findUserByEmail,
  getConversationState,
  updateConversationState
};
