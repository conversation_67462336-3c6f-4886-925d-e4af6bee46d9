name: ci
on: [pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - name: Install dependencies
        run: npm install
        env:
          NODE_AUTH_TOKEN: ${{secrets.NPM_TOKEN}}
      - name: Install Serverless CLI
        run: sudo npm i -g serverless@4.17.0
      - name: Configure AWS credentials
        run: serverless config credentials --provider aws --key ${{ secrets.AWS_ACCESS_KEY_ID_DEV }} --secret ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }} --profile dev
      - name: Build
        run: serverless package --stage dev --param="PROFILE=dev"
