Response:
{
  "success": true,
  "data": {
    "url": "https://payment-link-v3.pagar.me/pl_0exOZq2KLDYW3bf5NfLqGQrXlmzpvREa",
    "status": "created",
    "created_at": "2025-05-21T19:59:51.791Z",
    "pagarme_response": {
      "id": "pl_0exOZq2KLDYW3bf5NfLqGQrXlmzpvREa",
      "name": "Oca Tradicional - Jacque",
      "order_code": "pl_0exOZq2KLDYW3bf5NfLqGQrXlmzpvREa",
      "url": "https://payment-link-v3.pagar.me/pl_0exOZq2KLDYW3bf5NfLqGQrXlmzpvREa",
      "payment_link_type": "order",
      "status": "active",
      "expires_in": 0,
      "max_sessions": 0,
      "total_sessions": 0,
      "max_paid_sessions": 1,
      "total_paid_sessions": 0,
      "created_at": "2025-05-21T19:59:51.4753502Z",
      "updated_at": "2025-05-21T19:59:51.4753502Z",
      "payment_settings": {
        "accepted_payment_methods": [
          "credit_card",
          "pix"
        ],
        "accepted_multi_payment_methods": [],
        "statement_descriptor": "Oca AI",
        "credit_card_settings": {
          "operation_type": "auth_and_capture",
          "installments": [
            {
              "number": 1,
              "total": 110
            },
            {
              "number": 2,
              "total": 110
            }
          ]
        },
        "pix_settings": {
          "expires_in": 864000,
          "discount": 0,
          "discount_percentage": 0
        },
        "google_pay_enabled": false,
        "apple_pay_enabled": false,
        "threed_secure_enabled": false,
        "click_to_pay_enabled": false
      },
      "customer_settings": {
        "customer_id": "cus_yE9xqAETJTXqolmG",
        "editable": true
      },
      "cart_settings": {
        "shipping_cost": 0,
        "shipping_total_cost": 0,
        "items_total_cost": 110,
        "total_cost": 110,
        "items": [
          {
            "name": "Oca Tradicional",
            "description": "Crie sua pr\u00f3pria biografia de forma simples e r\u00e1pida com a ajuda do bi\u00f3grafo virtual. Conte sua hist\u00f3ria e n\u00f3s te ajudamos a escrev\u00ea-la.",
            "amount": 110,
            "shipping_cost": 0,
            "default_quantity": 1
          }
        ]
      },
      "layout_settings": {
        "primary_color": "#088037",
        "secondary_color": "#088037"
      },
      "flow_settings": {},
      "checkout_settings": {
        "accepted_brands": [
          "VISA",
          "MASTERCARD",
          "HIPERCARD",
          "DISCOVER",
          "DINERS",
          "AMEX",
          "ELO",
          "AURA",
          "JCB",
          "CABAL"
        ],
        "address_type": "brazilian",
        "enabled": true,
        "required_fields": [
          "customer.email",
          "customer.document",
          "customer.mobile_phone",
          "customer.name"
        ]
      },
      "account_settings": {
        "merchant_id": "merch_VK3R5AinZhwvRNa9",
        "account_id": "acc_Y9nPVMrcmmS7oNWw",
        "account_name": "Jacques Gomes Engenharia de Software",
        "display_name": "Jacques Gomes Engenharia de Software",
        "organization": "pagarme"
      }
    }
  }
}
