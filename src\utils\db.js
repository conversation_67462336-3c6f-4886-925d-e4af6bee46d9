require('dotenv').config();
const mysql = require('mysql2/promise');
const getConstant = require('@proak/constants');

let pool;

// Create a connection pool
const getPool = async () => {
  if (pool) return pool;
  
  console.log('Creating MySQL connection pool...');
  console.log('Database config:', {
    host: getConstant('mysql').MYSQL_HOST,
    port: getConstant('mysql').MYSQL_PORT,
    user: getConstant('mysql').MYSQL_USER,
    database: getConstant('mysql').MYSQL_DATABASE
  });
  
  // Create pool if it doesn't exist
  pool = mysql.createPool({
    host: getConstant('mysql').MYSQL_HOST || 'localhost',
    port: getConstant('mysql').MYSQL_PORT || 3306,
    user: getConstant('mysql').MYSQL_USER || 'root',
    password: getConstant('mysql').MYSQL_PASSWORD || '',
    database: getConstant('mysql').MYSQL_DATABASE || 'bookiadb',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0,
    acquireTimeout: 20000,  // 20 seconds to acquire connection
    timeout: 20000,         // 20 seconds for queries
    reconnect: true,
    connectTimeout: 20000,  // 20 seconds to connect
    ssl: false,
    multipleStatements: false
  });
  
  // Test the connection
  try {
    const connection = await pool.getConnection();
    console.log('Database connection successful');
    connection.release();
  } catch (error) {
    console.error('Database connection failed:', error);
    throw error;
  }
  
  return pool;
};

// Execute a query with parameters
const executeQuery = async (query, params = []) => {
  try {
    console.log('Executing query:', query);
    console.log('With params:', params);
    
    const connection = await getPool();
    const [rows] = await connection.execute(query, params);
    
    console.log('Query executed successfully, rows affected:', Array.isArray(rows) ? rows.length : 1);
    return rows;
  } catch (error) {
    console.error('Database error:', error);
    console.error('Query:', query);
    console.error('Params:', params);
    
    // Re-throw with more context
    const enhancedError = new Error(`Database query failed: ${error.message}`);
    enhancedError.originalError = error;
    enhancedError.query = query;
    enhancedError.params = params;
    throw enhancedError;
  }
};

// Get payment link by Pagar.me ID
const getPaymentLinkByPagarmeId = async (pagarmeId) => {
  return executeQuery(
    'SELECT * FROM payment_links WHERE external_id = ?',
    [pagarmeId]
  );
};

// Create a new payment link
const createPaymentLink = async (paymentData) => {
  const { user_id, plan_type, external_id, url, status, amount, metadata } = paymentData;
  
  // Ensure plan_type is a valid ENUM value
  let validPlanType = 'CUSTOM'; // Default
  
  // Validate plan_type against allowed ENUM values
  const allowedTypes = ['FREE', 'MONTHLY_STANDARD', 'MONTHLY_PREMIUM', 'ANNUAL_STANDARD', 'ANNUAL_PREMIUM', 'CUSTOM', 'PARTNER'];
  if (allowedTypes.includes(plan_type)) {
    validPlanType = plan_type;
  }
  
  // Calculate expiration date (default: 1 day)
  const expiresAt = new Date();
  expiresAt.setDate(expiresAt.getDate() + 1);
  
  return executeQuery(
    `INSERT INTO payment_links 
     (user_id, external_id, amount, plan_type, status, expires_at, url, name, type) 
     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
    [user_id, external_id, amount, validPlanType, status, expiresAt, url, 
     `Payment ${external_id.substr(0, 8)}`, 'ORDER']
  );
};

// Update payment link status
const updatePaymentLinkStatus = async (pagarmeId, status, paidAt = null) => {
  let query = 'UPDATE payment_links SET status = ?, updated_at = NOW()';
  let params = [status];
  
  if (paidAt) {
    query += ', paid_at = ?';
    params.push(paidAt);
  }
  
  query += ' WHERE external_id = ?';
  params.push(pagarmeId);
  
  return executeQuery(query, params);
};

// Activate user plan
const activateUserPlan = async (userId, planId, paymentId) => {
  // Get plan duration
  const plans = await executeQuery('SELECT * FROM plans WHERE id = ?', [planId]);
  const plan = plans[0];
  
  let endDate = null;
  if (plan && plan.duration_days > 0) {
    // Calculate end date based on plan duration
    endDate = new Date();
    endDate.setDate(endDate.getDate() + plan.duration_days);
  }
  
  // Check if user plan exists
  const existingPlans = await executeQuery(
    'SELECT * FROM user_plans WHERE user_id = ? AND plan_id = ? AND status != "expired"',
    [userId, planId]
  );
  
  if (existingPlans && existingPlans.length > 0) {
    // Update existing plan
    return executeQuery(
      `UPDATE user_plans SET 
       status = 'active', 
       payment_id = ?, 
       start_date = NOW(), 
       end_date = ?, 
       updated_at = NOW() 
       WHERE id = ?`,
      [paymentId, endDate, existingPlans[0].id]
    );
  } else {
    // Create new user plan
    return executeQuery(
      `INSERT INTO user_plans 
       (user_id, plan_id, payment_id, status, start_date, end_date) 
       VALUES (?, ?, ?, 'active', NOW(), ?)`,
      [userId, planId, paymentId, endDate]
    );
  }
};

// Close the pool (useful for testing)
const closePool = async () => {
  if (pool) {
    await pool.end();
    pool = null;
    console.log('Database pool closed');
  }
};

module.exports = {
  executeQuery,
  getPaymentLinkByPagarmeId,
  createPaymentLink,
  updatePaymentLinkStatus,
  activateUserPlan,
  closePool
}; 