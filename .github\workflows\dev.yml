name: DEV

on:
  push:
    branches:
      - dev

jobs:
  build:
    # To encrypt credentials: openssl aes-256-cbc -pbkdf2 -e -in auth-ocabooks-firebase.json -out secret-env-credentials
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - name: Check file existence
        id: check_files
        uses: andstor/file-existence-action@v1
        with:
          files: 'package-lock.json'
      - name: Install Openssl
        run: |
          sudo apt-get update && sudo apt-get install -y openssl
      - name: Install store Credentials
        run: |
          mkdir .store
          openssl aes-256-cbc -pbkdf2 -d -k "${{ secrets.SECRET_KEY }}" -in ./credentials/store/secret-env-credentials -out ./.store/credentials.json
      - name: Remove Package Lock
        if: steps.check_files.outputs.files_exists == 'true'
        # Only runs if all of the files exists
        run: rm package-lock.json
      - name: Install dependencies
        run: npm install
        env:
          NODE_AUTH_TOKEN: ${{secrets.NPM_TOKEN}}
      - name: Install Serverless CLI
        run: sudo npm i -g serverless@4.17.1
      - name: Deploy API application
        run: SLS_DEBUG=* sls deploy --stage dev --region us-east-2 --param="PROFILE=dev" --verbose
        env:
          SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}
        timeout-minutes: 60
