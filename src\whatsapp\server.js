// server.js
const express = require('express');
const bodyParser = require('body-parser');
const cors = require('cors');
const mysql = require('mysql2/promise');
const admin = require('firebase-admin');
require('dotenv').config();

// Inicializar Firebase
admin.initializeApp({
  credential: admin.credential.cert({
    projectId: process.env.FIREBASE_PROJECT_ID,
    clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
    privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
  }),
});

const auth = admin.auth();
const app = express();

app.use(cors());
app.use(bodyParser.urlencoded({ extended: false }));
app.use(bodyParser.json());

const pool = mysql.createPool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
});

pool.getConnection()
  .then(() => console.log('✅ Conectado ao banco com sucesso'))
  .catch(err => console.error('❌ Falha ao conectar no banco:', err));

const sessionState = new Map();

app.post('/api/chatbot', async (req, res) => {
  const from = (req.body.From || '').replace('whatsapp:', '');
  const body = (req.body.Body || '').trim();

  console.log(`📩 Mensagem de ${from}: ${body}`);

  const state = sessionState.get(from) || {};

  try {
    if (!state.user_id) {
      // Tentar buscar user_id na tabela users por email
      // Buscar usuário no Firebase (apenas para log/debug)
try {
  let userRecord;
  try {
    userRecord = await auth.getUserByEmail(body);
  } catch (e) {
    if (e.code === 'auth/user-not-found') {
      userRecord = await auth.getUser(body); // Tenta por UID
    } else {
      throw e;
    }
  }

  console.log('📄 Firebase Auth:');
  console.log('UID:', userRecord.uid);
  console.log('Email:', userRecord.email);
  console.log('Email Verificado:', userRecord.emailVerified);
  console.log('Data de Criação:', userRecord.metadata.creationTime);
  console.log('Último Login:', userRecord.metadata.lastSignInTime);
  console.log('Provedor(es):', userRecord.providerData.map(p => p.providerId).join(', '));
} catch (firebaseError) {
  console.error('❌ Firebase Auth - erro:', firebaseError.message);
}

// Agora buscar user_id no MySQL
const [rows] = await pool.query('SELECT id FROM users WHERE email = ?', [body]);
if (rows.length === 0) {
  await sendTwilioMessage(from, 'Email não encontrado. Tente novamente.');
  return res.end();
}

      if (rows.length === 0) {
        await sendTwilioMessage(from, 'Email não encontrado. Tente novamente.');
        return res.end();
      }

      state.user_id = rows[0].id;
      sessionState.set(from, state);
      await sendTwilioMessage(from, 'Qual capítulo deseja atualizar? (ex: 1, 2...)');
      return res.end();
    }

    if (!state.chapter_id) {
      const chapterId = parseInt(body);
      if (isNaN(chapterId)) {
        await sendTwilioMessage(from, 'Por favor, envie um número de capítulo válido.');
        return res.end();
      }

      state.chapter_id = chapterId;
      sessionState.set(from, state);
      await sendTwilioMessage(from, `O que deseja adicionar ao capítulo ${chapterId}?`);
      return res.end();
    }

    if (!state.message_text) {
      const messageText = body;

      await pool.query(
        'INSERT INTO messages (user_id, chapter_id, message_text) VALUES (?, ?, ?)',
        [state.user_id, state.chapter_id, messageText]
      );

      await sendTwilioMessage(from, '✅ Sua mensagem foi adicionada com sucesso. Obrigado!');
      
      // Limpar o estado para reiniciar o fluxo
      sessionState.delete(from);
      return res.end();
    }

  } catch (error) {
    console.error('❌ Erro no webhook:', error);
    await sendTwilioMessage(from, 'Erro interno. Tente novamente mais tarde.');
    res.status(500).end();
  }
});

async function sendTwilioMessage(to, body) {
  try {
    const accountSid = process.env.TWILIO_SID;
    const authToken = process.env.TWILIO_AUTH_TOKEN;
    const client = require('twilio')(accountSid, authToken);

    await client.messages.create({
      body,
      from: process.env.TWILIO_PHONE,
      to: `whatsapp:${to}`,
    });

    console.log('Mensagem enviada para:', to);
  } catch (error) {
    console.error('Erro ao enviar mensagem Twilio:', error);
  }
}

// Endpoints de debug
app.get('/api/usuarios', async (req, res) => {
  try {
    const [usuarios] = await pool.query('SELECT id, email FROM users');
    res.json(usuarios);
  } catch (error) {
    console.error('Erro ao buscar usuários:', error);
    res.status(500).json({ error: 'Erro interno' });
  }
});

app.get('/api/firebase-auth-users', async (req, res) => {
  try {
    const listUsersResult = await auth.listUsers();
    const users = listUsersResult.users.map(user => ({
      uid: user.uid,
      email: user.email,
      emailVerified: user.emailVerified,
      creationTime: user.metadata.creationTime,
      lastSignInTime: user.metadata.lastSignInTime,
      lastRefreshTime: user.metadata.lastRefreshTime
    }));
    
    res.json(users);
  } catch (error) {
    console.error('Erro ao buscar usuários do Firebase Auth:', error);
    res.status(500).json({ error: 'Erro interno' });
  }
});

app.listen(process.env.PORT || 3000, () => console.log(`Rodando na porta ${process.env.PORT || 3000}`));