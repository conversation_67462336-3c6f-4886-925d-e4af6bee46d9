# Deploy Instructions

## Ambientes Disponíveis

### Produção (prod)
- **Arquivo de configuração**: `.env.prod`
- **Stage**: `prod`
- **URL base**: `https://{api-id}.execute-api.us-east-2.amazonaws.com/prod`

### Desenvolvimento (dev)
- **Arquivo de configuração**: `.env.dev`
- **Stage**: `dev`
- **URL base**: `https://{api-id}.execute-api.us-east-2.amazonaws.com/dev`

## Comandos de Deploy

### Deploy para Produção
```bash
npx serverless deploy --stage prod --region us-east-2
```

### Deploy para Desenvolvimento
```bash
npx serverless deploy --stage dev --region us-east-2
```

### Deploy Local (desenvolvimento)
```bash
npx serverless offline --stage dev
```

## Gerenciamento dos Ambientes

### Listar deployments
```bash
# Ver stacks da AWS
npx serverless info --stage prod --region us-east-2
npx serverless info --stage dev --region us-east-2
```

### Remover deployment
```bash
# Remover stack de produção
npx serverless remove --stage prod --region us-east-2

# Remover stack de desenvolvimento
npx serverless remove --stage dev --region us-east-2
```

## Estrutura de Arquivos de Configuração

- `.env.prod` → Configurações de produção
- `.env.dev` → Configurações de desenvolvimento
- `.env.example` → Template para novos ambientes

## Notas Importantes

1. Cada stage cria uma stack separada na AWS
2. Os recursos são isolados entre ambientes
3. As URLs das APIs são diferentes para cada ambiente
4. Os logs são separados por stage 