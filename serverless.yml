# "org" ensures this Service is used with the correct Serverless Framework Access Key.
org: ocabooks
# "app" enables Serverless Framework Dashboard features and sharing them with other Services.
app: oca-api
service: oca-api

provider:
  name: aws
  runtime: nodejs20.x
  stage: ${opt:stage, 'dev'}
  region: ${opt:region, 'us-east-2'}
  timeout: 30
  environment:
    PROFILE: ${self:provider.stage}
  httpApi:
    cors: true
  apiGateway:
    apiKeys:
      - api-key-${opt:stage}

plugins:
  - serverless-offline

stages:
  prod:
    domains: api.ocabooks.com
  dev:
    domains: api-dev.ocabooks.com

package:
  excludeDevDependencies: true
  individually: true
  patterns:
    - '!node_modules/.bin/**'
    - '!node_modules/serverless*/**'

functions:
  createPaymentLink:
    handler: src/handlers/paymentLink.create
    events:
      - httpApi:
          path: /payment-links
          method: POST
          private: false
    environment:
      LOG_LEVEL: info

  processWebhook:
    handler: src/handlers/webhook.process
    events:
      - httpApi:
          path: /webhooks/payment
          method: POST
    environment:
      LOG_LEVEL: info

custom:
  serverless-offline:
    httpPort: 4000
    noPrependStageInUrl: true
    lambdaPort: 3002
    timeout: 30
  dotenv:
    path: .env.${opt:stage, 'prod'}
