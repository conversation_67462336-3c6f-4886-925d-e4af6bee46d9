name: MAIN

on:
  push:
    branches:
      - prod

jobs:
  build:
    # openssl aes-256-cbc -e -in credentials.json -out secret-env-cipher -k $KEY -md sha256
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - name: Check file existence
        id: check_files
        uses: andstor/file-existence-action@v1
        with:
          files: 'package-lock.json'
      - name: Install Openssl
        run: |
          sudo apt-get update && sudo apt-get install -y openssl
      - name: Install store Credentials
        run: |
          mkdir .store
          openssl aes-256-cbc -pbkdf2 -d -k "${{ secrets.SECRET_KEY }}" -in ./credentials/store/secret-env-credentials -out ./.store/credentials.json
      - name: Remove Package Lock
        if: steps.check_files.outputs.files_exists == 'true'
        # Only runs if all of the files exists
        run: rm package-lock.json
      - name: Install dependencies
        run: npm install
        env:
          NODE_AUTH_TOKEN: ${{secrets.NPM_TOKEN}}
      - name: Install Serverless CLI
        run: sudo npm i -g serverless@4.17.1
      - name: Deploy API application
        run: SLS_DEBUG=* sls deploy --stage prod --region us-east-1 --param="PROFILE=prod" --verbose
        env:
          SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}
        timeout-minutes: 60
