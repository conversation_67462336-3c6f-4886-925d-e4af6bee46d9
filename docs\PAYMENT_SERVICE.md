# OCA-API Payment Service Documentation

## Overview
This document describes the serverless payment service implementation that handles payment link generation and webhook processing for the OCA platform using AWS Lambda and Pagar.me payment gateway.

## Architecture

### Serverless Functions
- **Payment Link Generator** (`src/handlers/paymentLink.js`)
- **Webhook Processor** (`src/handlers/webhook.js`)
- **Database**: MySQL with connection pooling
- **Payment Gateway**: Pagar.me API integration

## Database Schema

### payment_links Table
```sql
CREATE TABLE payment_links (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    plan_id INT,
    external_id VARCHAR(255) UNIQUE,  -- Pagar.me payment link ID
    amount DECIMAL(10,2),
    plan_type VARCHAR(50),
    status VARCHAR(50) DEFAULT 'PENDING',
    url VARCHAR(255),
    name VARCHAR(255),
    type VARCHAR(50),
    max_paid_sessions INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    paid_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_external_id (external_id),
    INDEX idx_status (status)
);
```

### user_plans Table
```sql
CREATE TABLE user_plans (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,
    payment_id INT,
    status VARCHAR(50) DEFAULT 'inactive',
    start_date TIMESTAMP,
    end_date TIMESTAMP NULL,  -- NULL for unlimited plans
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    UNIQUE KEY unique_user_plan (user_id, plan_id)
);
```

## Payment Link Generation (`src/handlers/paymentLink.js`)

### Function: `createPaymentLink`

#### Request Format
```json
{
  "planId": 2,
  "userId": 14,
  "userData": {
    "username": "Jacques",
    "email": "<EMAIL>"
  }
}
```

#### Process Flow
1. **Validation**
   - Verify user exists in database
   - Verify plan exists and is active
   - Check for existing pending payment links

2. **Duplicate Prevention**
   - Reuses existing PENDING payment links
   - Prevents multiple payment links for same user/plan

3. **Pagar.me Integration**
   - Creates payment link via Pagar.me API
   - Configures payment methods (Credit Card, PIX)
   - Sets up installment options (up to 2x interest-free)

#### Pagar.me Request Structure
```json
{
  "is_building": false,
  "payment_settings": {
    "credit_card_settings": {
      "installments_setup": {
        "interest_type": "simple",
        "max_installments": 2,
        "amount": 11000,
        "free_installments": 2,
        "interest_rate": 0
      },
      "operation_type": "auth_and_capture"
    },
    "pix_settings": {
      "expires_in": 864000,
      "additional_information": [
        {
          "name": "Oca Tradicional",
          "value": "Plan description"
        }
      ]
    },
    "accepted_payment_methods": ["credit_card", "pix"],
    "statement_descriptor": "Oca AI"
  },
  "customer_settings": {
    "customer": {
      "name": "Jacques",
      "email": "<EMAIL>",
      "code": "14",
      "type": "individual"
    }
  },
  "cart_settings": {
    "items": [
      {
        "name": "Oca Tradicional",
        "description": "Crie sua própria biografia...",
        "amount": 11000,
        "shipping_cost": 0,
        "default_quantity": 1
      }
    ]
  },
  "name": "Oca Tradicional - Jacques",
  "type": "order",
  "max_paid_sessions": 1
}
```

#### Database Storage
```sql
INSERT INTO payment_links 
(user_id, plan_id, external_id, amount, plan_type, status, url, name, type, max_paid_sessions) 
VALUES (14, 2, 'pl_RKPV4plgZG3dgNUWWUJ7jvQy0JxBAkOz', 11000, 'oca_tradicional', 'PENDING', 
        'https://payment-link-v3.pagar.me/pl_...', 'Oca Tradicional - Jacques', 'ORDER', 1);
```

#### Response Format
```json
{
  "success": true,
  "data": {
    "url": "https://payment-link-v3.pagar.me/pl_RKPV4plgZG3dgNUWWUJ7jvQy0JxBAkOz",
    "status": "created",
    "created_at": "2025-05-21T23:21:09.000Z"
  }
}
```

#### Error Handling
- User not found: 400 Bad Request
- Plan not found/inactive: 400 Bad Request
- Pagar.me API errors: 500 Internal Server Error
- Database errors: 500 Internal Server Error

## Webhook Processing (`src/handlers/webhook.js`)

### Function: `processWebhook`

#### Webhook Event Types
- **Processed**: `order.paid` - Payment completed successfully
- **Ignored**: Other webhook types (return 200 to prevent retries)

#### Sample Webhook Payload
```json
{
  "id": "wh_...",
  "type": "order.paid",
  "data": {
    "customer": {
      "email": "<EMAIL>"
    },
    "charges": [
      {
        "code": "pl_RKPV4plgZG3dgNUWWUJ7jvQy0JxBAkOz",
        "status": "paid"
      }
    ]
  }
}
```

#### Process Flow
1. **Validation**
   - Verify webhook type is `order.paid`
   - Extract payment code from charges
   - Validate webhook structure

2. **Database Transaction**
   ```sql
   BEGIN TRANSACTION;
   
   -- Find payment link with plan info
   SELECT pl.*, p.duration_days, p.plan_type, p.name 
   FROM payment_links pl
   JOIN plans p ON pl.plan_id = p.id
   WHERE pl.external_id = 'pl_RKPV4plgZG3dgNUWWUJ7jvQy0JxBAkOz';
   
   -- Update payment link status
   UPDATE payment_links 
   SET status = 'PAID', paid_at = NOW(), updated_at = NOW() 
   WHERE id = 10;
   
   -- Activate user plan (upsert)
   INSERT INTO user_plans 
   (user_id, plan_id, payment_id, status, start_date, end_date, created_at, updated_at)
   VALUES (14, 2, 10, 'active', NOW(), '2026-05-21', NOW(), NOW())
   ON DUPLICATE KEY UPDATE 
     status = 'active',
     payment_id = 10,
     start_date = NOW(),
     end_date = '2026-05-21',
     updated_at = NOW();
   
   COMMIT;
   ```

3. **End Date Calculation**
   - Plans with `duration_days > 0`: `start_date + duration_days`
   - Plans with `duration_days = 0`: `end_date = NULL` (unlimited)

#### Database Updates
1. **Payment Link Update**
   - Status: `PENDING` → `PAID`
   - Set `paid_at` timestamp
   - Update `updated_at` timestamp

2. **User Plan Activation**
   - Create or update user_plans record
   - Set status to `active`
   - Calculate end_date based on plan duration
   - Link to payment_links record

#### Response Format
```json
{
  "success": true,
  "message": "Payment processed successfully"
}
```

## Environment Variables

### Required Configuration
```bash
# MySQL Database
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_DATABASE=bookiadb

# Pagar.me API
PAGARME_API_URL=https://api.pagar.me/core/v5/
PAGARME_API_KEY=sk_test_...
```

## Error Handling Strategy

### Payment Link Generation
- **User Validation**: Return 400 with descriptive error
- **Plan Validation**: Return 400 with descriptive error
- **API Failures**: Return 500 with sanitized error
- **Database Errors**: Return 500 with generic error

### Webhook Processing
- **Invalid Webhooks**: Return 200 to prevent retries
- **Missing Data**: Return 200 to prevent retries
- **Database Errors**: Return 200 to prevent retries
- **Transaction Rollback**: Automatic on any error

## Security Considerations

### Authentication
- Pagar.me API uses Basic Auth with API key
- Database connections use connection pooling
- No user authentication required for webhooks (validated by Pagar.me)

### Data Protection
- Payment data stored securely
- PII logging minimized
- Database transactions ensure consistency
- Connection pooling prevents resource exhaustion

## Monitoring & Logging

### Key Metrics
- Payment link generation success rate
- Webhook processing success rate
- Database transaction success rate
- Average response times

### Logging Strategy
```javascript
// Minimal logging for security
console.log('Payment link request received:', { planId, userId });
console.log('Found plan and user:', { 
  plan: { id: plan.id, name: plan.name, price: plan.price },
  user: { id: user.id, username: user.username }
});
console.log(`Processing payment ${paymentCode} for ${customer.email}`);
```

## Performance Optimizations

### Database
- Connection pooling (max 10 connections)
- Efficient JOIN queries
- Single transaction for webhook processing
- Proper indexing on frequently queried columns

### API Integration
- Axios for HTTP requests with proper error handling
- Timeout configurations
- Retry logic for transient failures

## CORS Configuration
```javascript
headers: {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent,Accept',
  'Access-Control-Allow-Methods': 'OPTIONS,POST',
  'Access-Control-Allow-Credentials': true,
  'Content-Type': 'application/json'
}
```

## Deployment Notes

### AWS Lambda Configuration
- Runtime: Node.js 18.x
- Memory: 512 MB
- Timeout: 30 seconds
- Environment variables for database and Pagar.me

### Database Requirements
- MySQL 5.7+ or 8.0+
- Proper character set for international characters
- Regular backups and monitoring
- Connection limit monitoring 