import requests
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Serverless endpoint URL - use either local or production
ENVIRONMENT = os.environ.get("TEST_ENV", "local")  # Default to local

if ENVIRONMENT == "prod":
    api_base = "https://u5upt26ru7.execute-api.us-east-2.amazonaws.com"
    print("Using PRODUCTION serverless endpoint")
else:
    api_base = "http://localhost:4000"
    print("Using LOCAL serverless endpoint (npm run dev)")

# Make sure we don't have trailing slashes
if api_base.endswith('/'):
    api_base = api_base[:-1]

PAYMENT_API_URL = f"{api_base}/payment-links"

def test_payment_link_generation():
    # Simulated Firebase user data
    user_data = {
        "planId": 4,  # Oca Tradicional plan
        "userId": "apkKBkXVevMJjEYLLuuLFFeX3rU2",  # Firebase UID format
        "userData": {
            "username": "test_user",
            "email": "<EMAIL>",
            "displayName": "Test User"  # Firebase displayName
        }
    }

    print("\n=== Testing Payment Link Generation (Firebase UID) ===")
    print(f"Endpoint: {PAYMENT_API_URL}")
    print("\nRequest Payload:")
    print(json.dumps(user_data, indent=2))

    try:
        # Make request to serverless endpoint
        response = requests.post(
            PAYMENT_API_URL,
            json=user_data,
            headers={
                'Content-Type': 'application/json'
            },
            timeout=10
        )

        print(f"\nStatus Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("\nResponse:")
            print(json.dumps(data, indent=2))
            
            if data.get('success'):
                print("\n✅ Payment link generated successfully!")
                print(f"Payment URL: {data['data']['url']}")
                
                # Print for Firebase integration
                print("\nFirebase User Data Used:")
                print(f"Firebase UID: {user_data['userId']}")
                print(f"Username: {user_data['userData']['username']}")
                print(f"Email: {user_data['userData']['email']}")
            else:
                print("\n❌ Failed to generate payment link")
                print(f"Error: {data.get('error', 'Unknown error')}")
        else:
            print("\n❌ Request failed")
            print(f"Response: {response.text}")
            print("\nCheck if:")
            print("1. The serverless API is running (`cd oca-api && npm run dev`)")
            print("2. MySQL connection details are correct in .env")
            print("3. The plan_id exists in your database")
            print("4. The payment_links table supports Firebase UIDs (VARCHAR)")

    except requests.exceptions.ConnectionError:
        print("\n❌ Connection error")
        print(f"Could not connect to {PAYMENT_API_URL}")
        print("Make sure the serverless API is running and accessible.")
    except Exception as e:
        print("\n❌ Error occurred:")
        print(str(e))

if __name__ == "__main__":
    # To test with production endpoint:
    # TEST_ENV=prod python tests/test_payment_frontend.py
    test_payment_link_generation()