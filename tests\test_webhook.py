import requests
import json

# Use the local serverless endpoint
url = 'http://localhost:4000/webhooks/payment'

# Firebase UID example (instead of integer ID)
firebase_uid = "firebase_uid_test_user_12345"

# Sample webhook data with Firebase UID
webhook_payload = {
  "id": "hook_RyEKQO789TRpZjv5",
  "account": {
    "id": "acc_jZkdN857et650oNv",
    "name": "<PERSON>jinha"
  },
  "type": "order.paid",
  "created_at": "2017-06-29T20:23:47",
  "data": {
    "id": "or_ZdnB5BBCmYhk534R",
    "code": "1303724",
    "amount": 12356,
    "currency": "BRL",
    "closed": True,
    "items": [
      {
        "id": "oi_EqnMMrbFgBf0MaN1",
        "description": "Produto",
        "amount": 10166,
        "quantity": 1,
        "status": "active",
        "created_at": "2022-06-29T20:23:42",
        "updated_at": "2022-06-29T20:23:42"
      }
    ],
    "customer": {
      "id": "cus_oy23JRQCM1cvzlmD",
      "name": "FABIO ",
      "email": "<EMAIL>",
      "document": "***********",
      "type": "individual",
      "delinquent": False,
      "created_at": "2022-06-29T20:23:42",
      "updated_at": "2022-06-29T20:23:42",
      "code": firebase_uid,  # Firebase UID stored in Pagar.me customer code
      "phones": {}
    },
    "shipping": {
      "amount": 2190,
      "description": "Economico",
      "address": {
        "zip_code": "90265",
        "city": "Malibu",
        "state": "CA",
        "country": "US",
        "line_1": "10880, Malibu Point, Malibu Central"
      }
    },
    "status": "paid",
    "created_at": "2022-06-29T20:23:42",
    "updated_at": "2022-06-29T20:23:47",
    "closed_at": "2022-06-29T20:23:44",
    "charges": [
      {
        "id": "ch_d22356Jf4WuGr8no",
        "code": "1303624",  # This code should match external_id in payment_links table
        "gateway_id": "da7f2304-1937-42a4-b995-0f4ea2b36264",
        "amount": 12356,
        "status": "paid",
        "currency": "BRL",
        "payment_method": "credit_card",
        "paid_at": "2022-06-29T20:23:47",
        "created_at": "2022-06-29T20:23:42",
        "updated_at": "2022-06-29T20:23:47",
        "customer": {
          "id": "cus_oybzJRQ231cvzlmD",
          "name": "FABIO E RACHEL ",
          "email": "<EMAIL>",
          "document": "09006507709",
          "type": "individual",
          "delinquent": False,
          "created_at": "2022-06-29T20:23:42",
          "updated_at": "2022-06-29T20:23:42",
          "phones": {}
        },
        "last_transaction": {
          "id": "tran_opAqDj2390S1lKQO",
          "transaction_type": "credit_card",
          "gateway_id": "3b12320a-0d67-4c06-b497-6622fe9763c8",
          "amount": 12356,
          "status": "captured",
          "success": True,
          "installments": 2,
          "acquirer_name": "redecard",
          "acquirer_affiliation_code": "30233726",
          "acquirer_tid": "247391236",
          "acquirer_nsu": "247391236",
          "acquirer_auth_code": "236689",
          "operation_type": "capture",
          "card": {
            "id": "card_BjKOmahgAf0D23lw",
            "last_four_digits": "4485",
            "brand": "Visa",
            "holder_name": "FABIO",
            "exp_month": 6,
            "exp_year": 2025,
            "status": "active",
            "created_at": "2022-06-29T20:23:42",
            "updated_at": "2022-06-29T20:23:42",
            "billing_address": {
              "zip_code": "90265",
              "city": "Malibu",
              "state": "CA",
              "country": "US",
              "line_1": "10880, Malibu Point, Malibu Central"
            },
            "type": "credit"
          },
          "created_at": "2022-06-29T20:23:47",
          "updated_at": "2022-06-29T20:23:47",
          "gateway_response": {
            "code": "200"
          }
        }
      }
    ]
  }
}

print("=== Testing Webhook with Firebase UID ===")
print(f"Endpoint: {url}")
print(f"Firebase UID in customer.code: {firebase_uid}")
print(f"Payment code (external_id): {webhook_payload['data']['charges'][0]['code']}")

# Send the webhook data to our local endpoint
headers = {'Content-Type': 'application/json'}
response = requests.post(url, data=json.dumps(webhook_payload), headers=headers)

print(f"\nStatus code: {response.status_code}")
print(f"Response: {response.text}")

if response.status_code == 200:
    try:
        data = response.json()
        if data.get('success'):
            print("\n✅ Webhook processed successfully!")
            print("User plan should be activated with Firebase UID")
        else:
            print("\n❌ Webhook processing failed")
            print(f"Error: {data.get('error', 'Unknown error')}")
    except json.JSONDecodeError:
        print("\n⚠️ Response is not valid JSON")
else:
    print(f"\n❌ HTTP Error {response.status_code}")
    print("Check:")
    print("1. Serverless API is running: npm run dev")
    print("2. Database connection is working") 
    print("3. payment_links table has a record with the external_id")
    print("4. user_plans table supports Firebase UIDs")

# If the request was successful, you should see the simulated DB logs in the terminal
# where your serverless application is running 