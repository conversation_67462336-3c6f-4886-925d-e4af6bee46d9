{"id": "hook_4vXDBLwTXTaZJk8a", "account": {"id": "acc_Y9nPVMrcmmS7oNWw", "name": "<PERSON> Software"}, "type": "order.paid", "created_at": "2025-05-21T20:12:37.704426Z", "data": {"id": "or_PBWElExUQUzJkex3", "code": "pl_0exOZq2KLDYW3bf5NfLqGQrXlmzpvREa", "amount": 110, "currency": "BRL", "closed": true, "items": [{"id": "oi_AKnkylVtZtd1eGPO", "amount": 110, "code": "item_Q5qBYXo0zWNr0vRhmXFQRa4edMv9yR2w", "created_at": "2025-05-21T20:12:11.8266667Z", "description": "Oca Tradicional", "quantity": 1, "status": "active", "updated_at": "2025-05-21T20:12:11.8266667Z"}], "customer": {"id": "cus_yE9xqAETJTXqolmG", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "document": "***********", "document_type": "cpf", "type": "individual", "delinquent": false, "address": {"id": "addr_xQXy93qi6iANlaKL", "line_1": "750, Alameda dos Bosques, Parque do Jiqui", "street": "Alameda dos Bosques", "number": "750", "zip_code": "********", "neighborhood": "Parque do Jiqui", "city": "<PERSON><PERSON><PERSON><PERSON>", "state": "RN", "country": "BR", "status": "active", "created_at": "2025-05-18T19:26:52.333Z", "updated_at": "2025-05-18T19:26:52.333Z", "metadata": {}}, "created_at": "2025-05-18T19:26:42.677Z", "updated_at": "2025-05-20T01:35:17.08Z", "phones": {"mobile_phone": {"country_code": "55", "number": "994514529", "area_code": "84"}}, "metadata": {}}, "status": "paid", "created_at": "2025-05-21T20:12:11.827Z", "updated_at": "2025-05-21T20:12:37.6259178Z", "closed_at": "2025-05-21T20:12:11.827Z", "ip": "2804:14d:be8a:951e:4545:78dd:8901:dc9f", "charges": [{"id": "ch_LOoamaZHAHB9b7vD", "code": "pl_0exOZq2KLDYW3bf5NfLqGQrXlmzpvREa", "gateway_id": "**********", "amount": 110, "paid_amount": 110, "status": "paid", "currency": "BRL", "payment_method": "pix", "paid_at": "2025-05-21T20:12:37Z", "created_at": "2025-05-21T20:12:11.867Z", "updated_at": "2025-05-21T20:12:37.5946672Z", "pending_cancellation": false, "customer": {"id": "cus_yE9xqAETJTXqolmG", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "document": "***********", "document_type": "cpf", "type": "individual", "delinquent": false, "address": {"id": "addr_xQXy93qi6iANlaKL", "line_1": "750, Alameda dos Bosques, Parque do Jiqui", "street": "Alameda dos Bosques", "number": "750", "zip_code": "********", "neighborhood": "Parque do Jiqui", "city": "<PERSON><PERSON><PERSON><PERSON>", "state": "RN", "country": "BR", "status": "active", "created_at": "2025-05-18T19:26:52.333Z", "updated_at": "2025-05-18T19:26:52.333Z", "metadata": {}}, "created_at": "2025-05-18T19:26:42.677Z", "updated_at": "2025-05-20T01:35:17.08Z", "phones": {"mobile_phone": {"country_code": "55", "number": "994514529", "area_code": "84"}}, "metadata": {}}, "last_transaction": {"transaction_type": "pix", "pix_provider_tid": "**********", "qr_code": "00020101021226820014br.gov.bcb.pix2560pix.stone.com.br/pix/v2/e8d77a3f-5c4b-4bd9-af95-81080501b9c352040000530398654041.105802BR592559 270 055 <PERSON> 6014RIO DE JANEIRO62290525607419bf791d632222487ebd363040572", "qr_code_url": "https://api.pagar.me/core/v5/transactions/tran_vBEVKaYSASEKp3ba/qrcode?payment_method=pix", "end_to_end_id": "E********202505212012s0895c9e573", "payer": {"name": "<PERSON>", "document": "***511274**", "document_type": "cpf", "bank_account": {"bank_name": "NU PAGAMENTOS S.A. - INSTITUIÇÃO DE PAGAMENTO", "ispb": "********"}}, "expires_at": "2025-05-31T20:12:11Z", "id": "tran_qlNkAMoU1UYjwXPm", "gateway_id": "**********", "amount": 110, "status": "paid", "success": true, "created_at": "2025-05-21T20:12:37.5946672Z", "updated_at": "2025-05-21T20:12:37.5946672Z", "gateway_response": {}, "antifraud_response": {}, "metadata": {}}, "metadata": {}}], "integration": {"code": "pl_0exOZq2KLDYW3bf5NfLqGQrXlmzpvREa"}, "metadata": {}}}